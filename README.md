# Mason Embedded Checkout SDK

A universal embedded checkout solution that allows any third-party website to easily integrate e-commerce checkout functionality with minimal effort.

## 🚀 Features

- **One-line integration**: Add a single script tag to get a fully functional checkout
- **Multi-payment processor support**: Stripe, PayPal, Adyen, and more
- **Universal compatibility**: Works on any domain with any CSP configuration
- **PCI SAQ-A compliant**: No sensitive payment data touches your site
- **Mobile optimized**: Works seamlessly across all devices and browsers

## 📦 Packages

This is a monorepo containing the following packages:

- **`@mason/checkout-sdk`** - Browser SDK for publishers
- **`@mason/checkout-ui`** - React-based checkout iframe application  
- **`@mason/checkout-api`** - Server-side API for payment processing
- **`@mason/shared`** - Shared types and utilities

## 🏃‍♂️ Quick Start

### 1. Script Tag Integration (Easiest)

Add this to your HTML page:

```html
<script src="https://cdn.mason.ai/mason-checkout.js"
        data-psp="stripe"
        data-publishable-key="pk_live_xxx"
        data-cart='[{"sku":"abc123","qty":1,"price":29.99,"name":"Product Name"}]'
        defer></script>

<button onclick="MasonEC.open()">Buy Now</button>
```

### 2. NPM Integration

```bash
npm install @mason/checkout-sdk
```

```javascript
import { MasonCheckout } from '@mason/checkout-sdk';

const checkout = new MasonCheckout({
  psp: 'stripe',
  publishableKey: 'pk_live_xxx',
  cart: [
    { sku: 'abc123', qty: 1, price: 29.99, name: 'Product Name' }
  ]
});

// Add event listeners
checkout.on('checkout_complete', (data) => {
  console.log('Payment successful!', data);
});

// Open checkout
document.getElementById('buy-button').addEventListener('click', () => {
  checkout.open();
});
```

## 🛠️ Development Setup

### Prerequisites

- Node.js 18+
- pnpm 8+

### Installation

```bash
# Clone the repository
git clone https://github.com/mason/embed-checkout-sdk.git
cd embed-checkout-sdk

# Install dependencies
pnpm install

# Build all packages
pnpm build
```

### Development

```bash
# Start all packages in development mode
pnpm dev

# This will start:
# - SDK build in watch mode
# - Checkout UI on http://localhost:3001
# - API server on http://localhost:3000
# - Example site on http://localhost:3002
```

### Testing

```bash
# Run all tests
pnpm test

# Run E2E tests
pnpm test:e2e

# Type checking
pnpm typecheck

# Linting
pnpm lint
```

## 📖 Documentation

- [Integration Guide](./docs/integration.md)
- [API Reference](./docs/api-reference.md)
- [Payment Providers](./docs/payment-providers.md)
- [Security & Compliance](./docs/security.md)
- [Troubleshooting](./docs/troubleshooting.md)

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the API package:

```env
# Stripe
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# PayPal
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret

# Adyen
ADYEN_API_KEY=your_adyen_api_key
ADYEN_MERCHANT_ACCOUNT=your_merchant_account

# General
NODE_ENV=development
FRONTEND_URL=http://localhost:3001
```

## 🌍 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- iOS Safari 14+
- Android Chrome 90+

## 📋 Roadmap

- [x] **M0**: Repo scaffolding, CI/CD, Stripe skeleton
- [ ] **M1**: Stripe MVP (US) + docs
- [ ] **M2**: PayPal adapter & feature flag
- [ ] **M3**: Adyen adapter, wallet QA matrix
- [ ] **M4**: SDK public beta, partner kit
- [ ] **M5**: Geo-expansion EU/APAC, tax calc

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./CONTRIBUTING.md) for details.

## 📄 License

MIT License - see [LICENSE](./LICENSE) for details.

## 🆘 Support

- [Documentation](./docs/)
- [GitHub Issues](https://github.com/mason/embed-checkout-sdk/issues)
- [Discord Community](https://discord.gg/mason-dev)

---

Made with ❤️ by the Mason team
