# Integration Guide

This guide will walk you through integrating the Mason Checkout SDK into your website.

## Integration Methods

### 1. Script Tag Integration (Recommended)

The easiest way to get started is with a simple script tag:

```html
<script src="https://cdn.mason.ai/mason-checkout.js"
        data-psp="stripe"
        data-publishable-key="pk_live_xxx"
        data-cart='[{"sku":"abc123","qty":1,"price":29.99,"name":"Product Name"}]'
        defer></script>

<button onclick="MasonEC.open()">Buy Now</button>
```

#### Script Tag Attributes

| Attribute | Required | Description |
|-----------|----------|-------------|
| `data-psp` | Yes | Payment provider: `stripe`, `paypal`, or `adyen` |
| `data-publishable-key` | Yes | Your payment provider's publishable key |
| `data-cart` | No | JSON array of initial cart items |
| `data-api-url` | No | Custom API URL (defaults to `https://api.mason.ai`) |
| `data-iframe-url` | No | Custom iframe URL (defaults to `https://shop.mason.ai`) |
| `data-debug` | No | Enable debug mode (`true` or `false`) |

### 2. NPM Integration

For more control, install via NPM:

```bash
npm install @mason/checkout-sdk
```

```javascript
import { MasonCheckout } from '@mason/checkout-sdk';

const checkout = new MasonCheckout({
  psp: 'stripe',
  publishableKey: 'pk_live_xxx',
  cart: [
    { sku: 'abc123', qty: 1, price: 29.99, name: 'Product Name' }
  ],
  debug: true
});
```

## Cart Management

### Adding Items

```javascript
// Add a single item
checkout.addItem({
  sku: 'product-123',
  qty: 1,
  price: 29.99,
  name: 'Product Name',
  description: 'Product description',
  image: 'https://example.com/image.jpg'
});

// Add multiple items
checkout.addItem({ sku: 'item-1', qty: 2, price: 15.00 });
checkout.addItem({ sku: 'item-2', qty: 1, price: 25.00 });
```

### Removing Items

```javascript
// Remove specific item
checkout.removeItem('product-123');

// Clear entire cart
checkout.clearCart();
```

### Getting Cart Contents

```javascript
const cart = checkout.getCart();
console.log('Cart items:', cart.items);
console.log('Total:', cart.total);
```

## Event Handling

Listen for checkout events to handle success, errors, and user actions:

```javascript
// Payment successful
checkout.on('checkout_complete', (data) => {
  console.log('Payment completed:', data);
  // Redirect to success page, show confirmation, etc.
});

// Payment failed
checkout.on('checkout_error', (error) => {
  console.error('Payment failed:', error);
  // Show error message, retry option, etc.
});

// User cancelled checkout
checkout.on('checkout_cancel', () => {
  console.log('User cancelled checkout');
  // Handle cancellation
});

// Checkout ready (iframe loaded)
checkout.on('checkout_ready', (data) => {
  console.log('Checkout ready:', data);
});

// Checkout closed
checkout.on('checkout_close', () => {
  console.log('Checkout closed');
});
```

## Opening Checkout

```javascript
// Open checkout modal
try {
  await checkout.open();
} catch (error) {
  console.error('Failed to open checkout:', error);
}
```

## Content Security Policy (CSP)

Add these directives to your CSP header:

```
frame-src https://shop.mason.ai https://*.stripe.com https://www.paypal.com https://*.adyen.com;
script-src https://cdn.mason.ai https://js.stripe.com https://www.paypal.com;
connect-src https://api.mason.ai https://api.stripe.com https://api.paypal.com;
```

## Error Handling

```javascript
// Handle initialization errors
try {
  const checkout = new MasonCheckout(config);
} catch (error) {
  console.error('SDK initialization failed:', error);
}

// Handle checkout opening errors
checkout.open().catch(error => {
  console.error('Failed to open checkout:', error);
  // Show user-friendly error message
});
```

## Testing

### Test Mode

Use test keys for development:

```javascript
const checkout = new MasonCheckout({
  psp: 'stripe',
  publishableKey: 'pk_test_...', // Test key
  debug: true // Enable debug logging
});
```

### Test Cards

For Stripe testing, use these test card numbers:

- **Success**: `****************`
- **Decline**: `****************`
- **3D Secure**: `****************`

## Best Practices

1. **Always validate cart data** before opening checkout
2. **Handle all event types** for better user experience
3. **Use HTTPS** in production
4. **Test with different payment methods** and scenarios
5. **Implement proper error handling** and user feedback
6. **Keep your publishable keys secure** (never expose secret keys)

## Next Steps

- [API Reference](./api-reference.md)
- [Payment Providers](./payment-providers.md)
- [Security Guide](./security.md)
