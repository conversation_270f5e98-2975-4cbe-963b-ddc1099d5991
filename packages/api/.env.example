# Environment Configuration
NODE_ENV=development
PORT=3000

# Frontend URLs
FRONTEND_URL=http://localhost:3001

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# PayPal Configuration
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret

# Adyen Configuration
ADYEN_API_KEY=your_adyen_api_key
ADYEN_MERCHANT_ACCOUNT=your_merchant_account
ADYEN_CLIENT_KEY=your_adyen_client_key

# Security
JWT_SECRET=your_jwt_secret_for_cart_tokens

# Database (if needed for order storage)
DATABASE_URL=postgresql://user:password@localhost:5432/mason_checkout
