{"name": "@mason/checkout-api", "version": "0.1.0", "description": "Mason Checkout API - server-side functions", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "vitest"}, "dependencies": {"@mason/shared": "workspace:*", "cors": "^2.8.5", "express": "^4.18.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.0", "stripe": "^14.0.0"}, "devDependencies": {"@types/cors": "^2.8.0", "@types/express": "^4.17.0", "@types/jsonwebtoken": "^9.0.0", "@types/node": "^20.10.0", "tsx": "^4.6.0", "typescript": "^5.3.0", "vitest": "^1.0.0"}, "keywords": ["checkout", "payments", "api", "stripe", "paypal"], "author": "<PERSON>", "license": "MIT"}