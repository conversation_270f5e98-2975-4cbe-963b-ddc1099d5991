import { Request, Response } from 'express';
import { makeAdyenPayment, submitAdyenPaymentDetails } from '../providers/adyen';

export const adyenPaymentHandler = {
  async payment(req: Request, res: Response) {
    try {
      const paymentData = req.body;

      if (!paymentData) {
        return res.status(400).json({
          success: false,
          error: 'Missing payment data',
        });
      }

      const payment = await makeAdyenPayment(paymentData);

      res.json({
        success: true,
        payment,
      });

    } catch (error: any) {
      console.error('Adyen payment error:', error);
      
      res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  },

  async details(req: Request, res: Response) {
    try {
      const details = req.body;

      if (!details) {
        return res.status(400).json({
          success: false,
          error: 'Missing payment details',
        });
      }

      const result = await submitAdyenPaymentDetails(details);

      res.json({
        success: true,
        result,
      });

    } catch (error: any) {
      console.error('Adyen payment details error:', error);
      
      res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  },
};
