import { Request, Response } from 'express';
import { createPayPalOrder, capturePayPalOrder, getPayPalOrder } from '../providers/paypal';

export const paypalOrderHandler = {
  async create(req: Request, res: Response) {
    try {
      const { cart, returnUrl, cancelUrl } = req.body;

      if (!cart) {
        return res.status(400).json({
          success: false,
          error: 'Missing cart data',
        });
      }

      const order = await createPayPalOrder(cart, { returnUrl, cancelUrl });

      res.json({
        success: true,
        order,
      });

    } catch (error: any) {
      console.error('PayPal order creation error:', error);
      
      res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  },

  async capture(req: Request, res: Response) {
    try {
      const { orderId } = req.body;

      if (!orderId) {
        return res.status(400).json({
          success: false,
          error: 'Missing order ID',
        });
      }

      const capture = await capturePayPalOrder(orderId);

      res.json({
        success: true,
        capture,
      });

    } catch (error: any) {
      console.error('PayPal order capture error:', error);
      
      res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  },

  async get(req: Request, res: Response) {
    try {
      const { orderId } = req.params;

      if (!orderId) {
        return res.status(400).json({
          success: false,
          error: 'Missing order ID',
        });
      }

      const order = await getPayPalOrder(orderId);

      res.json({
        success: true,
        order,
      });

    } catch (error: any) {
      console.error('PayPal order retrieval error:', error);
      
      res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  },
};
