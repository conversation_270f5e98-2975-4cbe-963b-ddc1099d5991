import { Request, Response } from 'express';
import type { CreateSessionRequest, CreateSessionResponse } from '@mason/shared';
import { validateCart, generateSessionId } from '@mason/shared';
import { createStripeSession } from '../providers/stripe';
import { createPayPalOrder } from '../providers/paypal';
import { createAdyenSession } from '../providers/adyen';

export async function createSessionHandler(req: Request, res: Response) {
  try {
    const { cart, provider, returnUrl, cancelUrl }: CreateSessionRequest = req.body;

    // Validate request
    if (!cart || !provider) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: cart, provider',
      } as CreateSessionResponse);
    }

    if (!validateCart(cart)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid cart data',
      } as CreateSessionResponse);
    }

    // Create session based on provider
    let session;
    
    switch (provider) {
      case 'stripe':
        session = await createStripeSession(cart, { returnUrl, cancelUrl });
        break;
      case 'paypal':
        session = await createPayPalOrder(cart, { returnUrl, cancelUrl });
        break;
      case 'adyen':
        session = await createAdyenSession(cart, { returnUrl, cancelUrl });
        break;
      default:
        return res.status(400).json({
          success: false,
          error: `Unsupported payment provider: ${provider}`,
        } as CreateSessionResponse);
    }

    // Return session
    res.json({
      success: true,
      session: {
        id: session.id,
        clientSecret: session.clientSecret,
        provider,
        cart,
        expiresAt: Date.now() + (60 * 60 * 1000), // 1 hour
      },
    } as CreateSessionResponse);

  } catch (error: any) {
    console.error('Create session error:', error);
    
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to create checkout session',
    } as CreateSessionResponse);
  }
}
