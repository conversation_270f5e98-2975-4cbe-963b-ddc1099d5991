import { Request, Response } from 'express';
import { handleStripeWebhook } from '../providers/stripe';

export async function stripeWebhookHandler(req: Request, res: Response) {
  try {
    const signature = req.headers['stripe-signature'] as string;
    
    if (!signature) {
      return res.status(400).json({
        success: false,
        error: 'Missing Stripe signature',
      });
    }

    // Handle the webhook
    const result = await handleStripeWebhook(req.body, signature);
    
    res.json(result);
  } catch (error: any) {
    console.error('Stripe webhook handler error:', error);
    
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
}
