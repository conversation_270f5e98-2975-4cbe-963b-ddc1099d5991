import Stripe from 'stripe';
import type { Cart } from '@mason/shared';
import { calculateCartTotal } from '@mason/shared';

// Initialize Stripe with secret key from environment
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || 'sk_test_placeholder', {
  apiVersion: '2023-10-16',
});

interface SessionOptions {
  returnUrl?: string;
  cancelUrl?: string;
}

export async function createStripeSession(cart: Cart, options: SessionOptions = {}) {
  try {
    // Convert cart items to Stripe line items
    const lineItems: Stripe.Checkout.SessionCreateParams.LineItem[] = cart.items.map(item => ({
      price_data: {
        currency: cart.currency.toLowerCase(),
        product_data: {
          name: item.name || item.sku,
          description: item.description,
          images: item.image ? [item.image] : undefined,
        },
        unit_amount: Math.round((item.price || 0) * 100), // Convert to cents
      },
      quantity: item.qty,
    }));

    // Create Stripe Checkout Session with embedded UI mode
    const session = await stripe.checkout.sessions.create({
      ui_mode: 'embedded',
      line_items: lineItems,
      mode: 'payment',
      return_url: options.returnUrl || `${process.env.FRONTEND_URL || 'http://localhost:3001'}/success?session_id={CHECKOUT_SESSION_ID}`,
      automatic_tax: { enabled: false }, // Enable if you have tax calculation set up
      payment_method_types: ['card'],
      // Enable additional payment methods
      payment_method_options: {
        card: {
          request_three_d_secure: 'automatic',
        },
      },
      // Metadata for tracking
      metadata: {
        cart_items: JSON.stringify(cart.items.map(item => ({ sku: item.sku, qty: item.qty }))),
        source: 'mason_checkout_sdk',
      },
      // Customer data collection
      customer_creation: 'if_required',
      billing_address_collection: 'required',
      shipping_address_collection: {
        allowed_countries: ['US'], // Expand as needed
      },
    });

    return {
      id: session.id,
      clientSecret: session.client_secret!,
      url: session.url,
    };

  } catch (error: any) {
    console.error('Stripe session creation error:', error);
    throw new Error(`Failed to create Stripe session: ${error.message}`);
  }
}

export async function retrieveStripeSession(sessionId: string) {
  try {
    const session = await stripe.checkout.sessions.retrieve(sessionId, {
      expand: ['line_items', 'payment_intent'],
    });
    return session;
  } catch (error: any) {
    console.error('Stripe session retrieval error:', error);
    throw new Error(`Failed to retrieve Stripe session: ${error.message}`);
  }
}

export async function handleStripeWebhook(body: Buffer, signature: string) {
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
  
  if (!webhookSecret) {
    throw new Error('Stripe webhook secret not configured');
  }

  try {
    const event = stripe.webhooks.constructEvent(body, signature, webhookSecret);

    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object as Stripe.Checkout.Session);
        break;
      case 'checkout.session.async_payment_succeeded':
        await handleAsyncPaymentSucceeded(event.data.object as Stripe.Checkout.Session);
        break;
      case 'checkout.session.async_payment_failed':
        await handleAsyncPaymentFailed(event.data.object as Stripe.Checkout.Session);
        break;
      default:
        console.log(`Unhandled Stripe event type: ${event.type}`);
    }

    return { received: true };
  } catch (error: any) {
    console.error('Stripe webhook error:', error);
    throw new Error(`Webhook error: ${error.message}`);
  }
}

async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  console.log('Checkout completed:', session.id);
  
  // Here you would typically:
  // 1. Update your database with the order
  // 2. Send confirmation emails
  // 3. Trigger fulfillment processes
  // 4. Update inventory
  
  // For now, just log the completion
  console.log('Order details:', {
    sessionId: session.id,
    customerEmail: session.customer_details?.email,
    amountTotal: session.amount_total,
    currency: session.currency,
    paymentStatus: session.payment_status,
    metadata: session.metadata,
  });
}

async function handleAsyncPaymentSucceeded(session: Stripe.Checkout.Session) {
  console.log('Async payment succeeded:', session.id);
  // Handle delayed payment success (e.g., bank transfers)
}

async function handleAsyncPaymentFailed(session: Stripe.Checkout.Session) {
  console.log('Async payment failed:', session.id);
  // Handle delayed payment failure
}
