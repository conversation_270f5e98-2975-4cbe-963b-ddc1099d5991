import type { Cart } from '@mason/shared';
import { calculateCartTotal } from '@mason/shared';

interface SessionOptions {
  returnUrl?: string;
  cancelUrl?: string;
}

// Adyen API configuration
const ADYEN_API_KEY = process.env.ADYEN_API_KEY || 'test_api_key';
const ADYEN_MERCHANT_ACCOUNT = process.env.ADYEN_MERCHANT_ACCOUNT || 'test_merchant';
const ADYEN_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://checkout-live.adyen.com' 
  : 'https://checkout-test.adyen.com';

export async function createAdyenSession(cart: Cart, options: SessionOptions = {}) {
  try {
    const total = calculateCartTotal(cart.items);
    const amountInMinorUnits = Math.round(total * 100); // Convert to cents

    // Create Adyen session
    const sessionData = {
      merchantAccount: ADYEN_MERCHANT_ACCOUNT,
      amount: {
        currency: cart.currency.toUpperCase(),
        value: amountInMinorUnits,
      },
      reference: `mason_${Date.now()}`,
      returnUrl: options.returnUrl || `${process.env.FRONTEND_URL || 'http://localhost:3001'}/success`,
      countryCode: 'US',
      shopperLocale: 'en_US',
      lineItems: cart.items.map(item => ({
        id: item.sku,
        description: item.name || item.sku,
        quantity: item.qty,
        amountIncludingTax: Math.round((item.price || 0) * 100),
      })),
    };

    const response = await fetch(`${ADYEN_BASE_URL}/v70/sessions`, {
      method: 'POST',
      headers: {
        'X-API-Key': ADYEN_API_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(sessionData),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Adyen session creation failed: ${error}`);
    }

    const session = await response.json();

    return {
      id: session.id,
      clientSecret: session.sessionData,
      url: session.url,
    };

  } catch (error: any) {
    console.error('Adyen session creation error:', error);
    throw new Error(`Failed to create Adyen session: ${error.message}`);
  }
}

export async function makeAdyenPayment(paymentData: any) {
  try {
    const response = await fetch(`${ADYEN_BASE_URL}/v70/payments`, {
      method: 'POST',
      headers: {
        'X-API-Key': ADYEN_API_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        merchantAccount: ADYEN_MERCHANT_ACCOUNT,
        ...paymentData,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Adyen payment failed: ${error}`);
    }

    const payment = await response.json();
    return payment;

  } catch (error: any) {
    console.error('Adyen payment error:', error);
    throw new Error(`Failed to process Adyen payment: ${error.message}`);
  }
}

export async function submitAdyenPaymentDetails(details: any) {
  try {
    const response = await fetch(`${ADYEN_BASE_URL}/v70/payments/details`, {
      method: 'POST',
      headers: {
        'X-API-Key': ADYEN_API_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(details),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Adyen payment details submission failed: ${error}`);
    }

    const result = await response.json();
    return result;

  } catch (error: any) {
    console.error('Adyen payment details error:', error);
    throw new Error(`Failed to submit Adyen payment details: ${error.message}`);
  }
}
