import type { Cart } from '@mason/shared';
import { calculateCartTotal } from '@mason/shared';

interface SessionOptions {
  returnUrl?: string;
  cancelUrl?: string;
}

// PayPal API configuration
const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID || 'test';
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET || 'test';
const PAYPAL_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://api.paypal.com' 
  : 'https://api.sandbox.paypal.com';

async function getPayPalAccessToken(): Promise<string> {
  const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_CLIENT_SECRET}`).toString('base64');
  
  const response = await fetch(`${PAYPAL_BASE_URL}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${auth}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: 'grant_type=client_credentials',
  });

  if (!response.ok) {
    throw new Error('Failed to get PayPal access token');
  }

  const data = await response.json();
  return data.access_token;
}

export async function createPayPalOrder(cart: Cart, options: SessionOptions = {}) {
  try {
    const accessToken = await getPayPalAccessToken();
    const total = calculateCartTotal(cart.items);

    // Create PayPal order
    const orderData = {
      intent: 'CAPTURE',
      purchase_units: [{
        amount: {
          currency_code: cart.currency.toUpperCase(),
          value: total.toFixed(2),
          breakdown: {
            item_total: {
              currency_code: cart.currency.toUpperCase(),
              value: total.toFixed(2),
            },
          },
        },
        items: cart.items.map(item => ({
          name: item.name || item.sku,
          description: item.description || '',
          unit_amount: {
            currency_code: cart.currency.toUpperCase(),
            value: (item.price || 0).toFixed(2),
          },
          quantity: item.qty.toString(),
          sku: item.sku,
        })),
      }],
      application_context: {
        return_url: options.returnUrl || `${process.env.FRONTEND_URL || 'http://localhost:3001'}/success`,
        cancel_url: options.cancelUrl || `${process.env.FRONTEND_URL || 'http://localhost:3001'}/cancel`,
        brand_name: 'Mason Checkout',
        landing_page: 'BILLING',
        user_action: 'PAY_NOW',
      },
    };

    const response = await fetch(`${PAYPAL_BASE_URL}/v2/checkout/orders`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderData),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`PayPal order creation failed: ${error}`);
    }

    const order = await response.json();

    return {
      id: order.id,
      clientSecret: order.id, // PayPal uses order ID as the client reference
      url: order.links?.find((link: any) => link.rel === 'approve')?.href,
    };

  } catch (error: any) {
    console.error('PayPal order creation error:', error);
    throw new Error(`Failed to create PayPal order: ${error.message}`);
  }
}

export async function capturePayPalOrder(orderId: string) {
  try {
    const accessToken = await getPayPalAccessToken();

    const response = await fetch(`${PAYPAL_BASE_URL}/v2/checkout/orders/${orderId}/capture`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`PayPal order capture failed: ${error}`);
    }

    const capture = await response.json();
    return capture;

  } catch (error: any) {
    console.error('PayPal order capture error:', error);
    throw new Error(`Failed to capture PayPal order: ${error.message}`);
  }
}

export async function getPayPalOrder(orderId: string) {
  try {
    const accessToken = await getPayPalAccessToken();

    const response = await fetch(`${PAYPAL_BASE_URL}/v2/checkout/orders/${orderId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`PayPal order retrieval failed: ${error}`);
    }

    const order = await response.json();
    return order;

  } catch (error: any) {
    console.error('PayPal order retrieval error:', error);
    throw new Error(`Failed to retrieve PayPal order: ${error.message}`);
  }
}
