import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createSessionHandler } from './handlers/create-session';
import { stripeWebhookHandler } from './handlers/stripe-webhook';
import { paypalOrderHandler } from './handlers/paypal-order';
import { adyenPaymentHandler } from './handlers/adyen-payment';

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      frameSrc: ["'self'", "*"],
      frameAncestors: ["'self'", "*"],
    },
  },
}));

// CORS configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://shop.mason.ai', 'https://cdn.mason.ai']
    : true,
  credentials: true,
}));

// Body parsing middleware
app.use('/webhook/stripe', express.raw({ type: 'application/json' }));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Checkout session creation
app.post('/create-session', createSessionHandler);

// Webhook handlers
app.post('/webhook/stripe', stripeWebhookHandler);

// PayPal handlers
app.post('/create-paypal-order', paypalOrderHandler.create);
app.post('/capture-paypal-order', paypalOrderHandler.capture);

// Adyen handlers
app.post('/payments/adyen', adyenPaymentHandler.payment);
app.post('/payments/details', adyenPaymentHandler.details);

// Apple Pay domain verification (for wallet support)
app.get('/.well-known/apple-developer-merchantid-domain-association', (req, res) => {
  res.sendFile('apple-developer-merchantid-domain-association', {
    root: './public/.well-known/',
    headers: {
      'Content-Type': 'text/plain'
    }
  });
});

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('API Error:', err);
  
  res.status(err.status || 500).json({
    success: false,
    error: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : err.message,
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`Mason Checkout API running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
});

export default app;
