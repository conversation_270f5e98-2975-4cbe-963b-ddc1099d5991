import type {
  SDKConfig,
  Cart,
  CartItem,
  CheckoutEvent,
  CheckoutEventType,
} from '@mason/shared';
import {
  encodeCartToken,
  calculateCartTotal,
  validateCart,
  safePostMessage,
} from '@mason/shared';

export class MasonCheckout {
  private config: SDKConfig;
  private cart: Cart;
  private iframe: HTMLIFrameElement | null = null;
  private eventListeners: Map<CheckoutEventType, Function[]> = new Map();
  private isOpen = false;

  constructor(config: SDKConfig) {
    this.config = {
      apiUrl: 'https://api.mason.ai',
      iframeUrl: 'https://shop.mason.ai',
      debug: false,
      ...config,
    };

    // Initialize cart from config or empty
    this.cart = {
      items: config.cart || [],
      currency: 'USD',
    };

    this.cart.total = calculateCartTotal(this.cart.items);

    // Set up message listener
    this.setupMessageListener();

    if (this.config.debug) {
      console.log('MasonCheckout initialized:', this.config);
    }
  }

  /**
   * Add item to cart
   */
  addItem(item: CartItem): void {
    const existingIndex = this.cart.items.findIndex(
      (existing) => existing.sku === item.sku
    );

    if (existingIndex >= 0) {
      this.cart.items[existingIndex].qty += item.qty;
    } else {
      this.cart.items.push(item);
    }

    this.cart.total = calculateCartTotal(this.cart.items);
    this.saveCartToStorage();

    if (this.config.debug) {
      console.log('Item added to cart:', item, 'Total items:', this.cart.items.length);
    }
  }

  /**
   * Remove item from cart
   */
  removeItem(sku: string): void {
    this.cart.items = this.cart.items.filter((item) => item.sku !== sku);
    this.cart.total = calculateCartTotal(this.cart.items);
    this.saveCartToStorage();

    if (this.config.debug) {
      console.log('Item removed from cart:', sku);
    }
  }

  /**
   * Clear cart
   */
  clearCart(): void {
    this.cart.items = [];
    this.cart.total = 0;
    this.saveCartToStorage();

    if (this.config.debug) {
      console.log('Cart cleared');
    }
  }

  /**
   * Get current cart
   */
  getCart(): Cart {
    return { ...this.cart };
  }

  /**
   * Open checkout
   */
  async open(): Promise<void> {
    if (this.isOpen) {
      if (this.config.debug) {
        console.log('Checkout already open');
      }
      return;
    }

    if (!validateCart(this.cart)) {
      throw new Error('Invalid cart data');
    }

    if (this.cart.items.length === 0) {
      throw new Error('Cart is empty');
    }

    try {
      // Create checkout session
      const session = await this.createCheckoutSession();
      
      // Create and show iframe
      this.createIframe(session.clientSecret);
      this.isOpen = true;

      if (this.config.debug) {
        console.log('Checkout opened with session:', session.id);
      }
    } catch (error) {
      console.error('Failed to open checkout:', error);
      throw error;
    }
  }

  /**
   * Close checkout
   */
  close(): void {
    if (this.iframe) {
      this.iframe.remove();
      this.iframe = null;
    }
    this.isOpen = false;

    this.emit('checkout_close', {});

    if (this.config.debug) {
      console.log('Checkout closed');
    }
  }

  /**
   * Add event listener
   */
  on(event: CheckoutEventType, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  /**
   * Remove event listener
   */
  off(event: CheckoutEventType, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private async createCheckoutSession(): Promise<any> {
    const cartToken = encodeCartToken(this.cart);
    
    const response = await fetch(`${this.config.apiUrl}/create-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        cart: this.cart,
        provider: this.config.psp,
        cartToken,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to create checkout session: ${response.statusText}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to create checkout session');
    }

    return result.session;
  }

  private createIframe(clientSecret: string): void {
    // Create overlay
    const overlay = document.createElement('div');
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
    `;

    // Create iframe
    this.iframe = document.createElement('iframe');
    const iframeUrl = `${this.config.iframeUrl}/ec?clientSecret=${clientSecret}&psp=${this.config.psp}`;
    
    this.iframe.src = iframeUrl;
    this.iframe.style.cssText = `
      width: 90%;
      max-width: 500px;
      height: 80%;
      max-height: 600px;
      border: none;
      border-radius: 8px;
      background: white;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    `;
    
    this.iframe.setAttribute('sandbox', 'allow-forms allow-scripts allow-same-origin');
    this.iframe.setAttribute('title', 'Mason Checkout');

    overlay.appendChild(this.iframe);
    document.body.appendChild(overlay);

    // Close on overlay click
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        this.close();
      }
    });

    // Handle escape key
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        this.close();
        document.removeEventListener('keydown', handleEscape);
      }
    };
    document.addEventListener('keydown', handleEscape);
  }

  private setupMessageListener(): void {
    window.addEventListener('message', (event) => {
      // Verify origin in production
      if (this.config.debug || event.origin === new URL(this.config.iframeUrl!).origin) {
        this.handleMessage(event.data);
      }
    });
  }

  private handleMessage(data: CheckoutEvent): void {
    if (this.config.debug) {
      console.log('Received message:', data);
    }

    switch (data.type) {
      case 'checkout_complete':
        this.handleCheckoutComplete(data.payload);
        break;
      case 'checkout_error':
        this.handleCheckoutError(data.payload);
        break;
      case 'checkout_cancel':
        this.close();
        break;
      default:
        // Handle other events
        break;
    }

    this.emit(data.type, data.payload);
  }

  private handleCheckoutComplete(payload: any): void {
    // Clear cart on successful checkout
    this.clearCart();
    this.close();

    // Send analytics event
    if (typeof window !== 'undefined' && (window as any).dataLayer) {
      (window as any).dataLayer.push({
        event: 'mason_ec_complete',
        amount: payload.amount,
        psp: this.config.psp,
      });
    }
  }

  private handleCheckoutError(payload: any): void {
    console.error('Checkout error:', payload);
    // Keep checkout open for retry
  }

  private emit(event: CheckoutEventType, payload: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach((callback) => {
        try {
          callback(payload);
        } catch (error) {
          console.error('Error in event listener:', error);
        }
      });
    }
  }

  private saveCartToStorage(): void {
    try {
      localStorage.setItem('mason_cart', JSON.stringify(this.cart));
      
      // Broadcast to other tabs
      if (typeof BroadcastChannel !== 'undefined') {
        const channel = new BroadcastChannel('mason_cart');
        channel.postMessage({ type: 'cart_updated', cart: this.cart });
      }
    } catch (error) {
      if (this.config.debug) {
        console.warn('Failed to save cart to storage:', error);
      }
    }
  }

  private loadCartFromStorage(): void {
    try {
      const stored = localStorage.getItem('mason_cart');
      if (stored) {
        const cart = JSON.parse(stored);
        if (validateCart(cart)) {
          this.cart = cart;
          this.cart.total = calculateCartTotal(this.cart.items);
        }
      }
    } catch (error) {
      if (this.config.debug) {
        console.warn('Failed to load cart from storage:', error);
      }
    }
  }
}
