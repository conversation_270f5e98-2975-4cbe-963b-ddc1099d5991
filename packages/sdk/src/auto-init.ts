import { MasonCheckout } from './mason-checkout';
import type { SDKConfig, CartItem } from '@mason/shared';

/**
 * Auto-initialization script that runs when the SDK is loaded via script tag
 */
function autoInit(): void {
  // Find the script tag that loaded this SDK
  const scripts = document.querySelectorAll('script[src*="mason-checkout"]');
  const scriptTag = scripts[scripts.length - 1] as HTMLScriptElement;

  if (!scriptTag) {
    console.warn('Mason Checkout: Could not find script tag for auto-initialization');
    return;
  }

  // Extract configuration from data attributes
  const config: Partial<SDKConfig> = {};
  
  const psp = scriptTag.getAttribute('data-psp');
  const publishableKey = scriptTag.getAttribute('data-publishable-key');
  const cartData = scriptTag.getAttribute('data-cart');
  const apiUrl = scriptTag.getAttribute('data-api-url');
  const iframeUrl = scriptTag.getAttribute('data-iframe-url');
  const debug = scriptTag.getAttribute('data-debug');

  if (!psp || !publishableKey) {
    console.error('Mason Checkout: Missing required data-psp or data-publishable-key attributes');
    return;
  }

  config.psp = psp as any;
  config.publishableKey = publishableKey;

  if (cartData) {
    try {
      config.cart = JSON.parse(cartData) as CartItem[];
    } catch (error) {
      console.error('Mason Checkout: Invalid cart data in data-cart attribute:', error);
      return;
    }
  }

  if (apiUrl) config.apiUrl = apiUrl;
  if (iframeUrl) config.iframeUrl = iframeUrl;
  if (debug) config.debug = debug === 'true';

  // Initialize the SDK
  try {
    const checkout = new MasonCheckout(config as SDKConfig);
    
    // Expose to global scope
    (window as any).MasonEC = checkout;

    // Dispatch ready event
    const readyEvent = new CustomEvent('mason-checkout-ready', {
      detail: { checkout }
    });
    document.dispatchEvent(readyEvent);

    if (config.debug) {
      console.log('Mason Checkout SDK initialized and ready');
    }
  } catch (error) {
    console.error('Mason Checkout: Failed to initialize SDK:', error);
  }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', autoInit);
} else {
  // DOM is already ready
  autoInit();
}
