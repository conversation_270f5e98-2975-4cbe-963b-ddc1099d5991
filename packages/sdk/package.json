{"name": "@mason/checkout-sdk", "version": "0.1.0", "description": "Mason Embedded Checkout SDK for publishers", "main": "./dist/mason-checkout.umd.js", "module": "./dist/mason-checkout.es.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/mason-checkout.es.js", "require": "./dist/mason-checkout.umd.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "vitest"}, "dependencies": {"@mason/shared": "workspace:*"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.0", "vite": "^5.0.0", "vite-plugin-dts": "^3.6.0", "vitest": "^1.0.0"}, "keywords": ["checkout", "payments", "sdk", "stripe", "paypal", "embedded"], "author": "<PERSON>", "license": "MIT"}