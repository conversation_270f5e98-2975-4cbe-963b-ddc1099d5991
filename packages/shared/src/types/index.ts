// Payment Service Provider types
export type PaymentProvider = 'stripe' | 'paypal' | 'adyen' | 'amazon-pay';

// Cart and Product types
export interface CartItem {
  sku: string;
  qty: number;
  price?: number;
  name?: string;
  description?: string;
  image?: string;
}

export interface Cart {
  items: CartItem[];
  currency: string;
  total?: number;
  metadata?: Record<string, any>;
}

// Checkout Session types
export interface CheckoutSession {
  id: string;
  clientSecret: string;
  provider: PaymentProvider;
  cart: Cart;
  expiresAt: number;
}

// SDK Configuration
export interface SDKConfig {
  psp: PaymentProvider;
  publishableKey: string;
  apiUrl?: string;
  iframeUrl?: string;
  cart?: CartItem[];
  debug?: boolean;
}

// Event types for postMessage communication
export type CheckoutEventType = 
  | 'checkout_ready'
  | 'checkout_complete'
  | 'checkout_error'
  | 'checkout_cancel'
  | 'checkout_close';

export interface CheckoutEvent {
  type: CheckoutEventType;
  payload?: any;
}

// API Response types
export interface CreateSessionRequest {
  cart: Cart;
  provider: PaymentProvider;
  returnUrl?: string;
  cancelUrl?: string;
}

export interface CreateSessionResponse {
  success: boolean;
  session?: CheckoutSession;
  error?: string;
}

// Webhook types
export interface WebhookEvent {
  id: string;
  type: string;
  provider: PaymentProvider;
  data: any;
  timestamp: number;
}
