import type { Cart, CartItem } from '../types';

/**
 * Generate a JWT-like token for cart data (simplified for demo)
 * In production, this should use proper JWT signing
 */
export function encodeCartToken(cart: Cart): string {
  const payload = {
    cart,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
  };
  
  // Simple base64 encoding for demo - use proper JWT in production
  return btoa(JSON.stringify(payload));
}

/**
 * Decode cart token
 */
export function decodeCartToken(token: string): Cart | null {
  try {
    const payload = JSON.parse(atob(token));
    
    // Check expiration
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return null;
    }
    
    return payload.cart;
  } catch {
    return null;
  }
}

/**
 * Calculate cart total
 */
export function calculateCartTotal(items: CartItem[]): number {
  return items.reduce((total, item) => {
    const price = item.price || 0;
    return total + (price * item.qty);
  }, 0);
}

/**
 * Validate cart items
 */
export function validateCart(cart: Cart): boolean {
  if (!cart.items || !Array.isArray(cart.items)) {
    return false;
  }
  
  return cart.items.every(item => 
    item.sku && 
    typeof item.sku === 'string' &&
    item.qty && 
    typeof item.qty === 'number' && 
    item.qty > 0
  );
}

/**
 * Generate unique session ID
 */
export function generateSessionId(): string {
  return `cs_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Safe postMessage wrapper
 */
export function safePostMessage(
  targetWindow: Window,
  message: any,
  targetOrigin: string = '*'
): void {
  try {
    targetWindow.postMessage(message, targetOrigin);
  } catch (error) {
    console.warn('Failed to send postMessage:', error);
  }
}
