{"name": "@mason/shared", "version": "0.1.0", "description": "Shared types and utilities for Mason Checkout SDK", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit"}, "devDependencies": {"typescript": "^5.3.0", "vite": "^5.0.0", "vite-plugin-dts": "^3.6.0"}, "keywords": ["checkout", "payments", "types"], "author": "<PERSON>", "license": "MIT"}