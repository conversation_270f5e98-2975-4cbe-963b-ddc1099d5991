{"name": "@mason/checkout-ui", "version": "0.1.0", "description": "Mason Checkout UI - iframe application", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "vitest"}, "dependencies": {"@mason/shared": "workspace:*", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.2.0", "typescript": "^5.3.0", "vite": "^5.0.0", "vitest": "^1.0.0"}, "keywords": ["checkout", "payments", "ui", "react"], "author": "<PERSON>", "license": "MIT"}