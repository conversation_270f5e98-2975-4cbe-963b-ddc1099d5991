import React, { createContext, useContext, ReactNode } from 'react';
import type { PaymentProvider } from '@mason/shared';

interface CheckoutContextType {
  provider: PaymentProvider;
  clientSecret: string;
  sendMessage: (type: string, payload?: any) => void;
}

const CheckoutContext = createContext<CheckoutContextType | null>(null);

interface CheckoutProviderProps {
  provider: PaymentProvider;
  clientSecret: string;
  children: ReactNode;
}

export function CheckoutProvider({ 
  provider, 
  clientSecret, 
  children 
}: CheckoutProviderProps) {
  const sendMessage = (type: string, payload?: any) => {
    if (window.parent) {
      window.parent.postMessage({
        type,
        payload
      }, '*');
    }
  };

  const value: CheckoutContextType = {
    provider,
    clientSecret,
    sendMessage,
  };

  return (
    <CheckoutContext.Provider value={value}>
      {children}
    </CheckoutContext.Provider>
  );
}

export function useCheckout() {
  const context = useContext(CheckoutContext);
  if (!context) {
    throw new Error('useCheckout must be used within a CheckoutProvider');
  }
  return context;
}
