import React, { useEffect, useState } from 'react';
import { CheckoutProvider } from './providers/CheckoutProvider';
import { CheckoutForm } from './components/CheckoutForm';
import { LoadingSpinner } from './components/LoadingSpinner';
import { ErrorMessage } from './components/ErrorMessage';
import type { PaymentProvider } from '@mason/shared';

function App() {
  const [clientSecret, setClientSecret] = useState<string>('');
  const [provider, setProvider] = useState<PaymentProvider>('stripe');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    // Parse URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const secret = urlParams.get('clientSecret');
    const psp = urlParams.get('psp') as PaymentProvider;

    if (!secret) {
      setError('Missing client secret');
      setLoading(false);
      return;
    }

    if (!psp) {
      setError('Missing payment provider');
      setLoading(false);
      return;
    }

    setClientSecret(secret);
    setProvider(psp);
    setLoading(false);

    // Notify parent that checkout is ready
    if (window.parent) {
      window.parent.postMessage({
        type: 'checkout_ready',
        payload: { provider: psp }
      }, '*');
    }
  }, []);

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  return (
    <CheckoutProvider provider={provider} clientSecret={clientSecret}>
      <div style={{ 
        padding: '20px', 
        height: '100vh', 
        display: 'flex', 
        flexDirection: 'column' 
      }}>
        <CheckoutForm />
      </div>
    </CheckoutProvider>
  );
}

export default App;
