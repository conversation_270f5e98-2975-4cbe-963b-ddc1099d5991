import React from 'react';

interface ErrorMessageProps {
  message: string;
}

export function ErrorMessage({ message }: ErrorMessageProps) {
  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      padding: '20px'
    }}>
      <div style={{
        background: '#fee',
        border: '1px solid #fcc',
        borderRadius: '8px',
        padding: '20px',
        textAlign: 'center',
        maxWidth: '400px'
      }}>
        <h3 style={{ 
          margin: '0 0 12px 0', 
          color: '#c33',
          fontSize: '18px'
        }}>
          Checkout Error
        </h3>
        <p style={{ 
          margin: 0, 
          color: '#666',
          fontSize: '14px'
        }}>
          {message}
        </p>
      </div>
    </div>
  );
}
