import React, { useEffect, useRef, useState } from 'react';
import { useCheckout } from '../../providers/CheckoutProvider';

declare global {
  interface Window {
    AdyenCheckout?: any;
  }
}

export function AdyenCheckout() {
  const { sendMessage } = useCheckout();
  const adyenRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    loadAdyen();
  }, []);

  const loadAdyen = async () => {
    try {
      // Load Adyen SDK
      if (!window.AdyenCheckout) {
        // Load CSS
        const cssLink = document.createElement('link');
        cssLink.rel = 'stylesheet';
        cssLink.href = 'https://checkoutshopper-live.adyen.com/checkoutshopper/sdk/5.0.0/adyen.css';
        document.head.appendChild(cssLink);

        // Load JS
        const script = document.createElement('script');
        script.src = 'https://checkoutshopper-live.adyen.com/checkoutshopper/sdk/5.0.0/adyen.js';
        script.onload = () => initializeAdyen();
        script.onerror = () => setError('Failed to load Adyen SDK');
        document.head.appendChild(script);
      } else {
        initializeAdyen();
      }
    } catch (err) {
      setError('Failed to initialize Adyen checkout');
      setLoading(false);
    }
  };

  const initializeAdyen = async () => {
    try {
      const checkout = await window.AdyenCheckout({
        environment: 'test', // Change to 'live' for production
        clientKey: 'test_CLIENT_KEY', // Replace with actual client key
        onSubmit: (state: any, dropin: any) => {
          // In production, submit to your API
          sendMessage('checkout_complete', {
            paymentData: state.data,
          });
        },
        onError: (error: any) => {
          setError('Adyen checkout error');
          sendMessage('checkout_error', { error });
        },
        paymentMethodsResponse: {
          // Mock payment methods - in production, get from your API
          paymentMethods: [
            {
              name: 'Credit Card',
              type: 'scheme'
            }
          ]
        }
      });

      const dropin = checkout.create('dropin').mount(adyenRef.current);
      setLoading(false);
    } catch (err: any) {
      setError(err.message || 'Failed to initialize Adyen checkout');
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        height: '200px',
        flexDirection: 'column',
        gap: '12px'
      }}>
        <div style={{
          width: '24px',
          height: '24px',
          border: '2px solid #f3f3f3',
          borderTop: '2px solid #0abf53',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }} />
        <p style={{ margin: 0, color: '#666', fontSize: '14px' }}>
          Loading Adyen checkout...
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        background: '#fee',
        border: '1px solid #fcc',
        borderRadius: '6px',
        padding: '16px',
        textAlign: 'center'
      }}>
        <p style={{ margin: 0, color: '#c33', fontSize: '14px' }}>
          {error}
        </p>
        <button
          onClick={() => window.location.reload()}
          style={{
            marginTop: '12px',
            padding: '8px 16px',
            background: '#0abf53',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div style={{ width: '100%' }}>
      <div ref={adyenRef} />
    </div>
  );
}
