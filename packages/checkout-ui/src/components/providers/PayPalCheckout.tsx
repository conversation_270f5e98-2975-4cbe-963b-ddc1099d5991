import React, { useEffect, useRef, useState } from 'react';
import { useCheckout } from '../../providers/CheckoutProvider';

declare global {
  interface Window {
    paypal?: any;
  }
}

export function PayPalCheckout() {
  const { sendMessage } = useCheckout();
  const paypalRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    loadPayPal();
  }, []);

  const loadPayPal = async () => {
    try {
      // Load PayPal SDK
      if (!window.paypal) {
        const script = document.createElement('script');
        script.src = 'https://www.paypal.com/sdk/js?client-id=test&currency=USD';
        script.onload = () => initializePayPal();
        script.onerror = () => setError('Failed to load PayPal SDK');
        document.head.appendChild(script);
      } else {
        initializePayPal();
      }
    } catch (err) {
      setError('Failed to initialize PayPal checkout');
      setLoading(false);
    }
  };

  const initializePayPal = () => {
    try {
      window.paypal.Buttons({
        createOrder: async () => {
          // In production, this would call your API to create a PayPal order
          // For now, return a mock order ID
          return 'MOCK_ORDER_ID';
        },
        onApprove: async (data: any) => {
          sendMessage('checkout_complete', {
            orderId: data.orderID,
            payerId: data.payerID,
          });
        },
        onCancel: () => {
          sendMessage('checkout_cancel', {});
        },
        onError: (err: any) => {
          setError('PayPal checkout error');
          sendMessage('checkout_error', { error: err });
        },
        style: {
          layout: 'vertical',
          color: 'blue',
          shape: 'rect',
          label: 'paypal'
        }
      }).render(paypalRef.current);

      setLoading(false);
    } catch (err: any) {
      setError(err.message || 'Failed to initialize PayPal checkout');
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        height: '200px',
        flexDirection: 'column',
        gap: '12px'
      }}>
        <div style={{
          width: '24px',
          height: '24px',
          border: '2px solid #f3f3f3',
          borderTop: '2px solid #0070ba',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }} />
        <p style={{ margin: 0, color: '#666', fontSize: '14px' }}>
          Loading PayPal checkout...
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        background: '#fee',
        border: '1px solid #fcc',
        borderRadius: '6px',
        padding: '16px',
        textAlign: 'center'
      }}>
        <p style={{ margin: 0, color: '#c33', fontSize: '14px' }}>
          {error}
        </p>
        <button
          onClick={() => window.location.reload()}
          style={{
            marginTop: '12px',
            padding: '8px 16px',
            background: '#0070ba',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div style={{ width: '100%' }}>
      <div ref={paypalRef} />
    </div>
  );
}
