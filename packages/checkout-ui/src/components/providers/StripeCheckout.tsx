import React, { useEffect, useRef, useState } from 'react';
import { useCheckout } from '../../providers/CheckoutProvider';

declare global {
  interface Window {
    Stripe?: any;
  }
}

export function StripeCheckout() {
  const { clientSecret, sendMessage } = useCheckout();
  const checkoutRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    loadStripe();
  }, []);

  const loadStripe = async () => {
    try {
      // Load Stripe.js
      if (!window.Stripe) {
        const script = document.createElement('script');
        script.src = 'https://js.stripe.com/v3/';
        script.onload = () => initializeStripe();
        script.onerror = () => setError('Failed to load Stripe');
        document.head.appendChild(script);
      } else {
        initializeStripe();
      }
    } catch (err) {
      setError('Failed to initialize Stripe checkout');
      setLoading(false);
    }
  };

  const initializeStripe = async () => {
    try {
      // Note: In production, you'd get the publishable key from your API
      // For now, we'll use a placeholder
      const stripe = window.Stripe('pk_test_placeholder');
      
      const checkout = await stripe.initEmbeddedCheckout({
        clientSecret: clientSecret,
      });

      if (checkoutRef.current) {
        checkout.mount(checkoutRef.current);
      }

      // Listen for checkout completion
      checkout.on('complete', (event: any) => {
        sendMessage('checkout_complete', {
          sessionId: event.session.id,
          amount: event.session.amount_total,
          currency: event.session.currency,
        });
      });

      setLoading(false);
    } catch (err: any) {
      setError(err.message || 'Failed to initialize Stripe checkout');
      setLoading(false);
      sendMessage('checkout_error', { error: err.message });
    }
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        height: '200px',
        flexDirection: 'column',
        gap: '12px'
      }}>
        <div style={{
          width: '24px',
          height: '24px',
          border: '2px solid #f3f3f3',
          borderTop: '2px solid #635bff',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }} />
        <p style={{ margin: 0, color: '#666', fontSize: '14px' }}>
          Loading Stripe checkout...
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        background: '#fee',
        border: '1px solid #fcc',
        borderRadius: '6px',
        padding: '16px',
        textAlign: 'center'
      }}>
        <p style={{ margin: 0, color: '#c33', fontSize: '14px' }}>
          {error}
        </p>
        <button
          onClick={() => window.location.reload()}
          style={{
            marginTop: '12px',
            padding: '8px 16px',
            background: '#635bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div style={{ width: '100%', minHeight: '400px' }}>
      <div ref={checkoutRef} />
    </div>
  );
}
