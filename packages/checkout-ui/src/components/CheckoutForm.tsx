import React from 'react';
import { useCheckout } from '../providers/CheckoutProvider';
import { StripeCheckout } from './providers/StripeCheckout';
import { PayPalCheckout } from './providers/PayPalCheckout';
import { AdyenCheckout } from './providers/AdyenCheckout';

export function CheckoutForm() {
  const { provider } = useCheckout();

  const renderCheckout = () => {
    switch (provider) {
      case 'stripe':
        return <StripeCheckout />;
      case 'paypal':
        return <PayPalCheckout />;
      case 'adyen':
        return <AdyenCheckout />;
      default:
        return (
          <div style={{ 
            padding: '20px', 
            textAlign: 'center',
            color: '#666'
          }}>
            <h3>Payment Provider Not Supported</h3>
            <p>The payment provider "{provider}" is not yet supported.</p>
          </div>
        );
    }
  };

  return (
    <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
      <div style={{ 
        borderBottom: '1px solid #e0e0e0', 
        paddingBottom: '16px', 
        marginBottom: '20px' 
      }}>
        <h2 style={{ margin: 0, fontSize: '20px', fontWeight: 600 }}>
          Complete Your Purchase
        </h2>
        <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '14px' }}>
          Secure checkout powered by {provider.charAt(0).toUpperCase() + provider.slice(1)}
        </p>
      </div>
      
      <div style={{ flex: 1 }}>
        {renderCheckout()}
      </div>
    </div>
  );
}
