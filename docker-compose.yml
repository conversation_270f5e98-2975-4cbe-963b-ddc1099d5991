version: '3.8'

services:
  # API Service
  api:
    build:
      context: .
      target: api
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - FRONTEND_URL=http://localhost:3001
    env_file:
      - packages/api/.env
    volumes:
      - ./packages/api/.env:/app/.env
    restart: unless-stopped

  # Checkout UI Service
  checkout-ui:
    build:
      context: .
      target: checkout-ui
    ports:
      - "3001:80"
    restart: unless-stopped

  # Example Site (for development)
  example:
    image: nginx:alpine
    ports:
      - "3002:80"
    volumes:
      - ./examples/basic-integration:/usr/share/nginx/html
      - ./packages/sdk/dist:/usr/share/nginx/html/sdk
    restart: unless-stopped

  # Redis (for session storage if needed)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

  # PostgreSQL (for order storage if needed)
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=mason_checkout
      - POSTGRES_USER=mason
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  postgres_data:
