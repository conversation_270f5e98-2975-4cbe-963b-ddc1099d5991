# Universal Embedded Checkout SDK – Technical Specification (v 0.9)

*(Scope: U.S. merchants; must work on **any** third‑party media site)*

---

## 1. Goals & Success Criteria

| Goal | KPI / Acceptance |
| --- | --- |
| **One‑line drop‑in** for widget developers | Copy‑paste `<script>` renders product widget **+** embedded checkout with no extra hosting steps |
| **Multi‑PSP support (U.S.)** | Stripe first‑class; fallback adapters for PayPal, Adyen, Amazon Pay (phase 2+) |
| **Publisher‑agnostic** | Works under arbitrary domains, CSPs, and cookie restrictions (Chrome 3P‑cookie sunset 2025) |
| **PCI SAQ‑A scope for publishers** | No secret keys, card PAN, or wallet tokens ever touch the host page’s JS context |
| **Time‑to‑first‑transaction < 3 min** | Measured from script copy → successful test charge in Stripe |

---

## 2. High‑Level Architecture

```mermaid
graph TD
    subgraph "Host Site (publisher.com)"
        A[HTML article / page] -->|loads| S["mason-checkout.js"]
        S -->|postMessage| IFrame
    end
    
    subgraph "IFrame (shop.mason.ai)"
        IFrame["Embedded Checkout<br/>(Stripe, PayPal, etc.)"]
    end
    
    S -->|REST/GraphQL| API[/Checkout Session API/]
    API -->|stripe.checkout.sessions.create| Stripe[Stripe]
    API -->|createOrder / capture| PayPal[PayPal]
    API --> Adyen[Adyen]
    Stripe --> WH[Webhook]
    WH --> API
```

- **Checkout UIs live inside an iframe** on `.mason.ai`, shielding publishers from PCI scope and CSP clashes.
- `mason‑checkout.js` = wrapper SDK injected by the publisher.
- **API layer** (edge function) creates checkout‑session tokens and processes webhooks.

---

## 3. Module‑by‑Module Detail

### 3.1 `mason‑checkout.js` (Browser SDK)

| Concern | Implementation |
| --- | --- |
| **Bootstrap** | UMD + ESM bundle ≤ 30 kB gz; injects `<iframe src="https://shop.mason.ai/ec?cartToken=..." sandbox="allow-forms allow-scripts allow-same-origin">`. |
| **Cart + UI hooks** | Exposes `window.MasonEC.addItem()`, `.open()`, `.on("success", cb)`; cart stored in `localStorage` + `BroadcastChannel`. |
| **Token fetch** | On `.open()`, calls `https://api.mason.ai/create-session?cart=JWT`. |
| **Comms** | Uses `postMessage` for `checkout_complete`, `checkout_abandon`, etc. |

### 3.2 Checkout Iframe (`/ec` app)

| Layer | Stripe Path (priority) | PayPal Path (phase 2) | Adyen Path (phase 3) |
| --- | --- | --- | --- |
| **Init** | `stripe.addEmbeddedCheckout({clientSecret})` | `paypal.Buttons({...}).render()` using `createOrder/captureOrder` | `new AdyenCheckout({...}).create('dropin').mount()` |
| **Events** | `checkout.complete` → parent | `onApprove`, `onCancel` → parent | `onSubmit`, `onComplete` → parent |
| **Wallet files** | Apple Pay domain file at `/.well-known/...`; Google Pay handled inside Stripe | N/A | Handles GPay/APay via Adyen components |

### 3.3 API Layer (Server‑side)

| Endpoint | Verb | Purpose |
| --- | --- | --- |
| `/create-session` | POST | Validate cart JWT → create **Stripe Checkout Session** (`ui_mode:'embedded'`) → return `{clientSecret}` |
| `/webhook/stripe` | POST | Listen for `checkout.session.completed`, `async_payment_succeeded` → fulfil order |
| `/create-paypal-order` | POST | Create PayPal `orderId`; return to iframe |
| `/capture-paypal-order` | POST | Capture approved PayPal order |
| `/payments/adyen` | POST | Proxy Adyen `/payments`; return `action` object |
| `/payments/details` | POST | Handle challenge & redirect flows for Adyen |

Deployment: pre‑built Docker image *or* Cloudflare Pages Functions; merchants supply `.env` with PSP keys.

---

## 4. Non‑Functional Requirements

| Area | Requirement |
| --- | --- |
| **Performance** | SDK added TTI ≤ 100 ms; iframe lazy‑loads until `.open()` |
| **Accessibility** | WCAG 2.1 AA – focus trap, aria‑labels |
| **Security** | CSP inside iframe: `frame-ancestors 'self' *`; parent CSP docs provided |
| **Analytics hooks** | `dataLayer.push({event:'mason_ec_complete', amount, psp})` emitted from SDK |
| **Browser support** | Evergreen Chrome/Edge/Firefox + iOS 15+ Safari, Android 13+ webview |

---

## 5. 3‑Party Host‑Site Considerations

| Challenge | Mitigation |
| --- | --- |
| **Strict CSP** | Document required directives (e.g. `frame‑src https://shop.mason.ai https://*.stripe.com https://www.paypal.com https://*.adyen.com`) |
| **3P cookie deprecation** | No cross‑site cookies; session state in JWT query string |
| **Ad blockers** | Serve assets from neutral domain `cdn.mason.ai`; avoid "ads" in paths |
| **Wallet domain validation** | All wallet flows occur inside iframe → single Apple Pay domain file for `shop.mason.ai` covers all publishers |

---

## 6. Vendor Priority & MVP Feature Set

| Priority | PSP | US Payment Methods (v1) | Additional Work (phase 2+) |
| --- | --- | --- | --- |
| **1** | **Stripe** | Card, Apple Pay, Google Pay, Link | ACH Debit, Klarna, Afterpay |
| **2** | **PayPal JS SDK** | PayPal Wallet, Venmo, Card | Pay Later, PayPal Credit |
| **3** | **Adyen Drop‑in** | Card, Apple Pay, Google Pay | EU local methods (iDeal, Sofort) |
| 4 | Amazon Pay BWP | Buy‑with‑Prime | Non‑Prime Amazon Pay, EU Prime |
| 5 | Bolt / Rapyd | Card + one‑tap | LATAM alt‑pays via Rapyd |

---

## 7. Sample Publisher Snippet (v1)

```html
<script src="https://cdn.mason.ai/mason-checkout.js"
        data-psp="stripe"
        data-publishable-key="pk_live_xxx"
        data-cart='[{"sku":"abc123","qty":1}]'
        defer></script>
<button onclick="MasonEC.open()">Buy Now</button>

```

---

## 8. Roadmap & Estimates

| Milestone | Scope | Owners | ETA |
| --- | --- | --- | --- |
| **M0** | Repo scaffolding, CI/CD, Stripe skeleton | 1 eng | 1 wk |
| **M1** | Stripe MVP (US) + docs | 2 eng | +2 wk |
| **M2** | PayPal adapter & feature flag | 1 eng | +1 wk |
| **M3** | Adyen adapter, wallet QA matrix | 2 eng | +2 wk |
| **M4** | SDK public beta, partner kit | PM + DevRel | +1 wk |
| **M5** | Geo‑expansion EU/APAC, tax calc | TBD | Q4 2025 |

---

## 9. Open Questions / Next Steps

1. **Order orchestration** – write orders back into Shopify/BigCommerce or keep a headless DB?
2. **Fraud & risk controls** – use PSP defaults in v1; evaluate ML scoring later.
3. **Subscription support** – switch to `mode:"subscription"` when needed.

---

*Prepared by:* Mason Dev · June 10 2025