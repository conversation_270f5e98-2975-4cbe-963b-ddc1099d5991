{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "baseUrl": ".", "paths": {"@mason/shared": ["./packages/shared/src"], "@mason/sdk": ["./packages/sdk/src"], "@mason/checkout-ui": ["./packages/checkout-ui/src"], "@mason/api": ["./packages/api/src"]}}, "include": ["packages/*/src/**/*", "packages/*/types/**/*"], "exclude": ["node_modules", "dist", "build"]}