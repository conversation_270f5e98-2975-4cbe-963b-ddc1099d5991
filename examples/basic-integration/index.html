<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mason Checkout SDK - Basic Example</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            display: flex;
            gap: 20px;
            align-items: center;
        }
        .product-image {
            width: 100px;
            height: 100px;
            background: #f0f0f0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
        .product-info {
            flex: 1;
        }
        .product-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 8px 0;
        }
        .product-price {
            font-size: 20px;
            font-weight: 700;
            color: #007cff;
            margin: 8px 0;
        }
        .btn {
            background: #007cff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: background 0.2s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .cart-summary {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .cart-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
        }
        .cart-total {
            font-weight: 700;
            font-size: 18px;
            border-top: 1px solid #ddd;
            padding-top: 12px;
            margin-top: 12px;
        }
        .checkout-btn {
            width: 100%;
            background: #28a745;
            font-size: 18px;
            padding: 16px;
        }
        .checkout-btn:hover {
            background: #218838;
        }
        .provider-selector {
            margin: 20px 0;
        }
        .provider-selector label {
            display: block;
            margin: 8px 0;
            cursor: pointer;
        }
        .provider-selector input[type="radio"] {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <h1>Mason Checkout SDK - Demo Store</h1>
    <p>This is a basic example showing how to integrate the Mason Checkout SDK into your website.</p>

    <!-- Payment Provider Selection -->
    <div class="provider-selector">
        <h3>Select Payment Provider:</h3>
        <label>
            <input type="radio" name="provider" value="stripe" checked>
            Stripe (Cards, Apple Pay, Google Pay)
        </label>
        <label>
            <input type="radio" name="provider" value="paypal">
            PayPal (PayPal Wallet, Venmo)
        </label>
        <label>
            <input type="radio" name="provider" value="adyen">
            Adyen (Cards, Wallets)
        </label>
    </div>

    <!-- Product Catalog -->
    <div class="product-card">
        <div class="product-image">📱</div>
        <div class="product-info">
            <h3 class="product-title">Premium Smartphone</h3>
            <p>Latest model with advanced features and sleek design.</p>
            <div class="product-price">$699.99</div>
        </div>
        <button class="btn" onclick="addToCart('phone-001', 'Premium Smartphone', 699.99)">
            Add to Cart
        </button>
    </div>

    <div class="product-card">
        <div class="product-image">💻</div>
        <div class="product-info">
            <h3 class="product-title">Laptop Pro</h3>
            <p>High-performance laptop for professionals and creators.</p>
            <div class="product-price">$1,299.99</div>
        </div>
        <button class="btn" onclick="addToCart('laptop-001', 'Laptop Pro', 1299.99)">
            Add to Cart
        </button>
    </div>

    <div class="product-card">
        <div class="product-image">🎧</div>
        <div class="product-info">
            <h3 class="product-title">Wireless Headphones</h3>
            <p>Premium noise-canceling headphones with superior sound quality.</p>
            <div class="product-price">$199.99</div>
        </div>
        <button class="btn" onclick="addToCart('headphones-001', 'Wireless Headphones', 199.99)">
            Add to Cart
        </button>
    </div>

    <!-- Cart Summary -->
    <div class="cart-summary">
        <h3>Shopping Cart</h3>
        <div id="cart-items">
            <p>Your cart is empty</p>
        </div>
        <div class="cart-total">
            Total: $<span id="cart-total">0.00</span>
        </div>
        <button class="btn checkout-btn" id="checkout-btn" onclick="openCheckout()" disabled>
            Checkout Now
        </button>
    </div>

    <!-- Mason Checkout SDK -->
    <script src="../../packages/sdk/dist/mason-checkout.umd.js"></script>
    
    <script>
        // Initialize cart
        let cart = [];
        let selectedProvider = 'stripe';

        // Listen for provider changes
        document.querySelectorAll('input[name="provider"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                selectedProvider = e.target.value;
                updateCheckoutSDK();
            });
        });

        // Initialize Mason Checkout SDK
        let masonCheckout;
        
        function initializeSDK() {
            try {
                masonCheckout = new MasonEC.MasonCheckout({
                    psp: selectedProvider,
                    publishableKey: 'pk_test_placeholder', // Replace with actual key
                    apiUrl: 'http://localhost:3000',
                    iframeUrl: 'http://localhost:3001',
                    debug: true,
                });

                // Set up event listeners
                masonCheckout.on('checkout_complete', (data) => {
                    alert('Payment successful! Thank you for your purchase.');
                    cart = [];
                    updateCartDisplay();
                    console.log('Checkout completed:', data);
                });

                masonCheckout.on('checkout_error', (error) => {
                    alert('Payment failed. Please try again.');
                    console.error('Checkout error:', error);
                });

                masonCheckout.on('checkout_cancel', () => {
                    console.log('Checkout cancelled by user');
                });

                console.log('Mason Checkout SDK initialized');
            } catch (error) {
                console.error('Failed to initialize Mason Checkout SDK:', error);
            }
        }

        function updateCheckoutSDK() {
            if (masonCheckout) {
                // Reinitialize with new provider
                initializeSDK();
            }
        }

        function addToCart(sku, name, price) {
            const existingItem = cart.find(item => item.sku === sku);
            
            if (existingItem) {
                existingItem.qty += 1;
            } else {
                cart.push({ sku, name, price, qty: 1 });
            }

            // Add to Mason Checkout SDK
            if (masonCheckout) {
                masonCheckout.addItem({ sku, name, price, qty: 1 });
            }

            updateCartDisplay();
        }

        function removeFromCart(sku) {
            cart = cart.filter(item => item.sku !== sku);
            
            // Remove from Mason Checkout SDK
            if (masonCheckout) {
                masonCheckout.removeItem(sku);
            }

            updateCartDisplay();
        }

        function updateCartDisplay() {
            const cartItemsEl = document.getElementById('cart-items');
            const cartTotalEl = document.getElementById('cart-total');
            const checkoutBtn = document.getElementById('checkout-btn');

            if (cart.length === 0) {
                cartItemsEl.innerHTML = '<p>Your cart is empty</p>';
                cartTotalEl.textContent = '0.00';
                checkoutBtn.disabled = true;
                return;
            }

            const total = cart.reduce((sum, item) => sum + (item.price * item.qty), 0);

            cartItemsEl.innerHTML = cart.map(item => `
                <div class="cart-item">
                    <span>${item.name} x ${item.qty}</span>
                    <span>
                        $${(item.price * item.qty).toFixed(2)}
                        <button onclick="removeFromCart('${item.sku}')" style="margin-left: 8px; background: #dc3545; color: white; border: none; padding: 2px 6px; border-radius: 3px; cursor: pointer;">×</button>
                    </span>
                </div>
            `).join('');

            cartTotalEl.textContent = total.toFixed(2);
            checkoutBtn.disabled = false;
        }

        function openCheckout() {
            if (!masonCheckout) {
                alert('Checkout SDK not initialized');
                return;
            }

            if (cart.length === 0) {
                alert('Your cart is empty');
                return;
            }

            try {
                masonCheckout.open();
            } catch (error) {
                console.error('Failed to open checkout:', error);
                alert('Failed to open checkout. Please try again.');
            }
        }

        // Initialize SDK when page loads
        document.addEventListener('DOMContentLoaded', () => {
            initializeSDK();
        });
    </script>
</body>
</html>
