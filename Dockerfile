# Multi-stage build for Mason Checkout SDK
FROM node:18-alpine AS base

# Install pnpm
RUN npm install -g pnpm

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json pnpm-workspace.yaml pnpm-lock.yaml ./
COPY packages/*/package.json ./packages/*/

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Build all packages
RUN pnpm build

# Production stage for API
FROM node:18-alpine AS api

RUN npm install -g pnpm

WORKDIR /app

# Copy built API package
COPY --from=base /app/packages/api/dist ./dist
COPY --from=base /app/packages/api/package.json ./
COPY --from=base /app/packages/shared/dist ./node_modules/@mason/shared/dist
COPY --from=base /app/packages/shared/package.json ./node_modules/@mason/shared/

# Install production dependencies only
RUN pnpm install --prod --frozen-lockfile

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S mason -u 1001

USER mason

EXPOSE 3000

CMD ["node", "dist/server.js"]

# Production stage for checkout UI
FROM nginx:alpine AS checkout-ui

# Copy built UI
COPY --from=base /app/packages/checkout-ui/dist /usr/share/nginx/html

# Copy nginx config
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
