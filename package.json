{"name": "@mason/embed-checkout-sdk", "version": "0.1.0", "description": "Universal Embedded Checkout SDK for third-party publishers", "private": true, "workspaces": ["packages/*"], "scripts": {"build": "pnpm -r build", "dev": "pnpm -r --parallel dev", "test": "pnpm -r test", "test:e2e": "playwright test", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "clean": "pnpm -r clean && rm -rf node_modules", "typecheck": "pnpm -r typecheck", "changeset": "changeset", "version-packages": "changeset version", "release": "pnpm build && changeset publish"}, "devDependencies": {"@changesets/cli": "^2.27.1", "@playwright/test": "^1.40.0", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.0", "typescript": "^5.3.0", "vite": "^5.0.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.10.0", "keywords": ["checkout", "payments", "stripe", "paypal", "embedded", "sdk", "ecommerce"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/mason/embed-checkout-sdk.git"}}